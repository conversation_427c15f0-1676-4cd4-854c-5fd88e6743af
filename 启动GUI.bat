@echo off
chcp 65001 >nul
title 进程监测管理器

echo ========================================
echo           进程监测管理器
echo ========================================
echo.

echo 正在检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python环境
    echo 请确保已安装Python 3.6或更高版本
    pause
    exit /b 1
)

echo Python环境检查通过
echo.

echo 正在启动GUI界面...
python run_gui.py

if errorlevel 1 (
    echo.
    echo 启动失败，请检查错误信息
    pause
)

echo.
echo 程序已退出
pause
