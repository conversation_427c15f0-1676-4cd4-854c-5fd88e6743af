import keyboard
import mouse
import time

print("按下 F9 开始监听鼠标点击，按下 F10 停止监听并退出。")

listening = False

# 鼠标点击事件回调
def on_click(event):
    if isinstance(event, mouse.ButtonEvent) and event.event_type == mouse.DOWN:
        x, y = mouse.get_position()
        print(f"鼠标点击坐标: ({x}, {y})")

while True:
    if keyboard.is_pressed('F9'):
        if not listening:
            print("开始监听鼠标点击。")
            listening = True
            mouse.hook(on_click)
        while keyboard.is_pressed('F9'):
            time.sleep(0.1)
    elif keyboard.is_pressed('F10'):
        if listening:
            print("停止监听，退出程序。")
            mouse.unhook(on_click)
            break
    time.sleep(0.05)
