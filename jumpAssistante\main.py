import keyboard
import pyautogui
import time
import random

# 指定两个点击位置（已根据获取到的坐标更新）
print("脚本已启动，按下数字键8会依次点击两个指定位置。按`退出。")

def get_random_positions():
    # 坐标1范围
    x1 = random.randint(1522, 1635)
    y1 = random.randint(17, 107)
    # 坐标2范围
    x2 = random.randint(990, 1348)
    y2 = random.randint(634, 698)
    return [(x1, y1), (x2, y2)]

while True:
    if keyboard.is_pressed('8'):
        positions = get_random_positions()
        for pos in positions:
            pyautogui.click(pos[0], pos[1])
            print(f"已点击位置 ({pos[0]}, {pos[1]})")
            time.sleep(random.uniform(0.1, 0.25))  # 两次点击间隔，随机0.1~0.25秒
        # 防止多次触发，等待按键松开
        while keyboard.is_pressed('8'):
            time.sleep(0.1)
    if keyboard.is_pressed('`'):
        print("退出脚本。")
        break
    time.sleep(0.05)