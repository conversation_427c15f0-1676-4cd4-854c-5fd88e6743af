import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import threading
import json
import subprocess
import time
import psutil
from process_monitor import (
    is_process_running, launch_and_wait, handle_special_task,
    all_mumu_not_running, all_mumu_running_count, quote_path
)

class ProcessMonitorGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("进程监测管理器")
        self.root.geometry("1000x700")
        self.root.configure(bg='#f0f0f0')
        
        # 加载配置
        self.load_config()
        
        # 创建主框架
        self.create_main_frame()
        
        # 运行状态
        self.is_running = False
        self.monitor_thread = None
        
    def load_config(self):
        """加载配置文件"""
        try:
            with open('config.json', 'r', encoding='utf-8') as f:
                self.config = json.load(f)
            self.workflows = self.config['workflows']
            self.pair_interval = self.config.get('pair_interval', 10)
        except Exception as e:
            messagebox.showerror("错误", f"加载配置文件失败: {e}")
            self.workflows = []
            self.pair_interval = 10
    
    def create_main_frame(self):
        """创建主界面框架"""
        # 创建左侧面板
        left_frame = tk.Frame(self.root, bg='#e8e8e8', width=250)
        left_frame.pack(side=tk.LEFT, fill=tk.Y, padx=5, pady=5)
        left_frame.pack_propagate(False)
        
        # 创建右侧面板
        right_frame = tk.Frame(self.root, bg='white')
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        self.create_left_panel(left_frame)
        self.create_right_panel(right_frame)
    
    def create_left_panel(self, parent):
        """创建左侧控制面板"""
        # 运行区域
        run_frame = tk.LabelFrame(parent, text="🔄 运行", font=('微软雅黑', 12, 'bold'), 
                                 bg='#e8e8e8', fg='#333')
        run_frame.pack(fill=tk.X, padx=10, pady=10)
        
        self.run_btn = tk.Button(run_frame, text="▶ 开始监测", font=('微软雅黑', 10),
                                bg='#4CAF50', fg='white', relief=tk.FLAT,
                                command=self.start_monitoring)
        self.run_btn.pack(fill=tk.X, padx=10, pady=10)
        
        # 等待区域
        wait_frame = tk.LabelFrame(parent, text="⏳ 等待", font=('微软雅黑', 12, 'bold'),
                                  bg='#e8e8e8', fg='#333')
        wait_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # 游戏列表
        games = ["启动游戏", "活跃奖励", "宿舍奖励", "远征"]
        self.game_buttons = []
        
        for game in games:
            btn_frame = tk.Frame(wait_frame, bg='#e8e8e8')
            btn_frame.pack(fill=tk.X, padx=10, pady=2)
            
            btn = tk.Button(btn_frame, text=game, font=('微软雅黑', 9),
                           bg='white', relief=tk.FLAT, anchor='w',
                           command=lambda g=game: self.handle_game_action(g))
            btn.pack(side=tk.LEFT, fill=tk.X, expand=True)
            
            settings_btn = tk.Button(btn_frame, text="⚙", font=('微软雅黑', 8),
                                   bg='#9C27B0', fg='white', width=3,
                                   command=lambda g=game: self.show_game_settings(g))
            settings_btn.pack(side=tk.RIGHT)
            
            self.game_buttons.append((btn, settings_btn))
        
        # 终止区域
        stop_frame = tk.LabelFrame(parent, text="🚫 终止", font=('微软雅黑', 12, 'bold'),
                                  bg='#e8e8e8', fg='#333')
        stop_frame.pack(fill=tk.X, padx=10, pady=10)
        
        self.stop_btn = tk.Button(stop_frame, text="⏹ 停止监测", font=('微软雅黑', 10),
                                 bg='#f44336', fg='white', relief=tk.FLAT,
                                 command=self.stop_monitoring)
        self.stop_btn.pack(fill=tk.X, padx=10, pady=10)
        
        # 状态显示
        status_frame = tk.LabelFrame(parent, text="📊 状态", font=('微软雅黑', 12, 'bold'),
                                    bg='#e8e8e8', fg='#333')
        status_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        self.status_text = tk.Text(status_frame, height=8, font=('Consolas', 9),
                                  bg='#2d2d2d', fg='#00ff00', wrap=tk.WORD)
        self.status_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
    
    def create_right_panel(self, parent):
        """创建右侧日志面板"""
        # 标题栏
        title_frame = tk.Frame(parent, bg='white', height=50)
        title_frame.pack(fill=tk.X)
        title_frame.pack_propagate(False)
        
        title_label = tk.Label(title_frame, text="📋 日志", font=('微软雅黑', 14, 'bold'),
                              bg='white', fg='#333')
        title_label.pack(side=tk.LEFT, padx=20, pady=15)
        
        clear_btn = tk.Button(title_frame, text="🗑 清空", font=('微软雅黑', 9),
                             bg='#FF9800', fg='white', relief=tk.FLAT,
                             command=self.clear_log)
        clear_btn.pack(side=tk.RIGHT, padx=20, pady=15)
        
        # 日志区域
        log_frame = tk.Frame(parent, bg='white')
        log_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=(0, 10))
        
        self.log_text = scrolledtext.ScrolledText(log_frame, font=('Consolas', 10),
                                                 bg='#fafafa', fg='#333',
                                                 wrap=tk.WORD, state=tk.DISABLED)
        self.log_text.pack(fill=tk.BOTH, expand=True)
    
    def log_message(self, message):
        """添加日志消息"""
        self.log_text.config(state=tk.NORMAL)
        timestamp = time.strftime("%H:%M:%S")
        self.log_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.log_text.see(tk.END)
        self.log_text.config(state=tk.DISABLED)
        self.root.update_idletasks()
    
    def update_status(self, status):
        """更新状态显示"""
        self.status_text.delete(1.0, tk.END)
        self.status_text.insert(tk.END, status)
        self.root.update_idletasks()
    
    def clear_log(self):
        """清空日志"""
        self.log_text.config(state=tk.NORMAL)
        self.log_text.delete(1.0, tk.END)
        self.log_text.config(state=tk.DISABLED)
    
    def start_monitoring(self):
        """开始监测"""
        if self.is_running:
            return
        
        self.is_running = True
        self.run_btn.config(state=tk.DISABLED, text="运行中...")
        self.stop_btn.config(state=tk.NORMAL)
        
        self.log_message("开始进程监测...")
        
        # 在新线程中运行监测
        self.monitor_thread = threading.Thread(target=self.run_monitoring, daemon=True)
        self.monitor_thread.start()
    
    def stop_monitoring(self):
        """停止监测"""
        self.is_running = False
        self.run_btn.config(state=tk.NORMAL, text="▶ 开始监测")
        self.stop_btn.config(state=tk.DISABLED)
        self.log_message("监测已停止")
    
    def run_monitoring(self):
        """运行监测逻辑"""
        try:
            # 检查MuMu任务状态
            if all_mumu_not_running(self.workflows):
                self.log_message("前四个MuMu任务均未运行，尝试执行一键启动批处理...")
                subprocess.Popen(r'E:\Date Of Computer\Desktop\Mard\yijian.bat', shell=True)
                self.log_message("已执行yijian.bat，等待60秒...")
                time.sleep(60)
            
            # 处理不可并行任务
            for wf in self.workflows:
                if not self.is_running:
                    break
                    
                if not wf.get('can_parallel', True):
                    self.process_workflow(wf, "不可并行任务")
            
            # 处理可并行任务
            for wf in self.workflows:
                if not self.is_running:
                    break
                    
                if wf.get('can_parallel', True):
                    self.process_workflow(wf, "可并行任务")
            
            self.log_message("所有任务处理完成")
            
        except Exception as e:
            self.log_message(f"监测过程中发生错误: {e}")
        finally:
            if self.is_running:
                self.stop_monitoring()
    
    def process_workflow(self, wf, task_type):
        """处理单个工作流"""
        # 等待MuMu任务完成
        while all_mumu_running_count(self.workflows) > 1 and wf not in self.workflows[:4]:
            if not self.is_running:
                return
            self.log_message("等待MuMu任务完成...")
            time.sleep(5)
        
        if is_process_running(wf['process_name']):
            self.log_message(f"{wf['name']} 已在运行，跳过")
            return
        
        self.log_message(f"启动{task_type}：{wf['name']}")
        
        if wf.get('special', False):
            handle_special_task(wf)
        else:
            launch_and_wait(wf['launch_cmd'], wf['process_name'], wf['window_title'])
            ast_cmd = wf.get('ast_cmd')
            if ast_cmd:
                self.log_message(f"启动AST：{ast_cmd}")
                subprocess.Popen(quote_path(ast_cmd), shell=True)
        
        self.log_message(f"等待{self.pair_interval}秒后继续...")
        time.sleep(self.pair_interval)
        
        # 更新状态显示
        running_processes = []
        for workflow in self.workflows:
            if is_process_running(workflow['process_name']):
                running_processes.append(f"✓ {workflow['name']}")
            else:
                running_processes.append(f"✗ {workflow['name']}")
        
        self.update_status("\n".join(running_processes))
    
    def handle_game_action(self, game_name):
        """处理游戏动作"""
        self.log_message(f"执行游戏动作: {game_name}")
        
        # 根据游戏名称执行相应操作
        if game_name == "启动游戏":
            self.start_monitoring()
        elif game_name == "活跃奖励":
            self.log_message("执行活跃奖励收集...")
        elif game_name == "宿舍奖励":
            self.log_message("执行宿舍奖励收集...")
        elif game_name == "远征":
            self.log_message("执行远征任务...")
    
    def show_game_settings(self, game_name):
        """显示游戏设置"""
        settings_window = tk.Toplevel(self.root)
        settings_window.title(f"{game_name} - 设置")
        settings_window.geometry("400x300")
        settings_window.configure(bg='white')
        
        tk.Label(settings_window, text=f"{game_name} 设置", 
                font=('微软雅黑', 14, 'bold'), bg='white').pack(pady=20)
        
        tk.Label(settings_window, text="这里可以配置具体的游戏参数", 
                bg='white').pack(pady=10)
        
        tk.Button(settings_window, text="保存设置", bg='#4CAF50', fg='white',
                 command=settings_window.destroy).pack(pady=20)

def main():
    root = tk.Tk()
    app = ProcessMonitorGUI(root)
    root.mainloop()

if __name__ == "__main__":
    main()
