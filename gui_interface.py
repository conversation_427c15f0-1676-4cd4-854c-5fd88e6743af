import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import threading
import json
import subprocess
import time
import psutil
from process_monitor import (
    is_process_running, launch_and_wait, handle_special_task,
    all_mumu_not_running, all_mumu_running_count, quote_path, find_window_by_title
)

class ProcessMonitorGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("进程监测管理器")
        self.root.geometry("1000x700")
        self.root.configure(bg='#f0f0f0')
        
        # 加载配置
        self.load_config()
        
        # 创建主框架
        self.create_main_frame()
        
        # 运行状态
        self.is_running = False
        self.monitor_thread = None

        # 启动时输出所有在运行中的任务及其PID到日志
        self.log_running_processes()

        # 初始化状态显示
        self.update_process_status()

        # 启动定时状态更新
        self.start_status_timer()
        
    
    def load_config(self):
        """加载配置文件"""
        try:
            with open('config.json', 'r', encoding='utf-8') as f:
                self.config = json.load(f)
            self.workflows = self.config['workflows']
            self.pair_interval = self.config.get('pair_interval', 10)
        except Exception as e:
            messagebox.showerror("错误", f"加载配置文件失败: {e}")
            self.workflows = []
            self.pair_interval = 10
    
    def create_main_frame(self):
        """创建主界面框架"""
        # 创建左侧面板
        left_frame = tk.Frame(self.root, bg='#e8e8e8', width=250)
        left_frame.pack(side=tk.LEFT, fill=tk.Y, padx=5, pady=5)
        left_frame.pack_propagate(False)
        
        # 创建右侧面板
        right_frame = tk.Frame(self.root, bg='white')
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        self.create_left_panel(left_frame)
        self.create_right_panel(right_frame)
    
    def create_left_panel(self, parent):
        """创建左侧控制面板"""
        # 运行区域
        run_frame = tk.LabelFrame(parent, text="🔄 运行", font=('微软雅黑', 12, 'bold'), 
                                 bg='#e8e8e8', fg='#333')
        run_frame.pack(fill=tk.X, padx=10, pady=10)
        
        self.run_btn = tk.Button(run_frame, text="▶ 开始监测", font=('微软雅黑', 10),
                                bg='#4CAF50', fg='white', relief=tk.FLAT,
                                command=self.start_monitoring)
        self.run_btn.pack(fill=tk.X, padx=10, pady=10)
        
        # 等待区域
        wait_frame = tk.LabelFrame(parent, text="⏳ 等待", font=('微软雅黑', 12, 'bold'),
                                  bg='#e8e8e8', fg='#333')
        wait_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # 从配置文件加载任务列表
        self.game_buttons = []

        for workflow in self.workflows:
            btn_frame = tk.Frame(wait_frame, bg='#e8e8e8')
            btn_frame.pack(fill=tk.X, padx=10, pady=2)

            # 根据任务类型设置不同的图标
            icon = "🎮" if workflow.get('special', False) else "📱"
            task_name = f"{icon} {workflow['name']}"

            btn = tk.Button(btn_frame, text=task_name, font=('微软雅黑', 9),
                           bg='white', relief=tk.FLAT, anchor='w',
                           command=lambda w=workflow: self.handle_workflow_action(w))
            btn.pack(side=tk.LEFT, fill=tk.X, expand=True)

            settings_btn = tk.Button(btn_frame, text="⚙", font=('微软雅黑', 8),
                                   bg='#9C27B0', fg='white', width=3,
                                   command=lambda w=workflow: self.show_workflow_settings(w))
            settings_btn.pack(side=tk.RIGHT)

            # 新增终止按钮
            kill_btn = tk.Button(
                btn_frame, text="⛔", font=('微软雅黑', 8),
                bg='#F44336', fg='white', width=3,
                command=lambda w=workflow: self.kill_workflow_process(w)
            )
            kill_btn.pack(side=tk.RIGHT, padx=(0, 5))

            self.game_buttons.append((btn, settings_btn, workflow))
        
        # 终止区域
        stop_frame = tk.LabelFrame(parent, text="🚫 终止", font=('微软雅黑', 12, 'bold'),
                                  bg='#e8e8e8', fg='#333')
        stop_frame.pack(fill=tk.X, padx=10, pady=10)
        
        self.stop_btn = tk.Button(stop_frame, text="⏹ 停止监测", font=('微软雅黑', 10),
                                 bg='#f44336', fg='white', relief=tk.FLAT,
                                 command=self.stop_monitoring)
        self.stop_btn.pack(fill=tk.X, padx=10, pady=10)
        
        # 状态显示
        status_frame = tk.LabelFrame(parent, text="📊 状态", font=('微软雅黑', 12, 'bold'),
                                    bg='#e8e8e8', fg='#333')
        status_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 刷新按钮
        refresh_btn = tk.Button(status_frame, text="🔄 刷新状态", font=('微软雅黑', 8),
                               bg='#2196F3', fg='white', relief=tk.FLAT,
                               command=self.update_process_status)
        refresh_btn.pack(fill=tk.X, padx=5, pady=(5, 0))

        self.status_text = tk.Text(status_frame, height=8, font=('Consolas', 9),
                                  bg='#2d2d2d', fg='#00ff00', wrap=tk.WORD)
        self.status_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
    
    def create_right_panel(self, parent):
        """创建右侧日志面板"""
        # 标题栏
        title_frame = tk.Frame(parent, bg='white', height=50)
        title_frame.pack(fill=tk.X)
        title_frame.pack_propagate(False)
        
        title_label = tk.Label(title_frame, text="📋 日志", font=('微软雅黑', 14, 'bold'),
                              bg='white', fg='#333')
        title_label.pack(side=tk.LEFT, padx=20, pady=15)
        
        clear_btn = tk.Button(title_frame, text="🗑 清空", font=('微软雅黑', 9),
                             bg='#FF9800', fg='white', relief=tk.FLAT,
                             command=self.clear_log)
        clear_btn.pack(side=tk.RIGHT, padx=20, pady=15)
        
        # 日志区域
        log_frame = tk.Frame(parent, bg='white')
        log_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=(0, 10))
        
        self.log_text = scrolledtext.ScrolledText(log_frame, font=('Consolas', 10),
                                                 bg='#fafafa', fg='#333',
                                                 wrap=tk.WORD, state=tk.DISABLED)
        self.log_text.pack(fill=tk.BOTH, expand=True)
    
    def log_message(self, message):
        """添加日志消息"""
        self.log_text.config(state=tk.NORMAL)
        timestamp = time.strftime("%H:%M:%S")
        self.log_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.log_text.see(tk.END)
        self.log_text.config(state=tk.DISABLED)
        self.root.update_idletasks()
    
    def update_status(self, status):
        """更新状态显示"""
        self.status_text.delete(1.0, tk.END)
        self.status_text.insert(tk.END, status)
        self.root.update_idletasks()

    def update_process_status(self):
        """更新进程状态显示"""
        running_processes = []
        for workflow in self.workflows:
            # 查找所有进程PID
            pids = [proc.info['pid'] for proc in psutil.process_iter(['name', 'pid']) if proc.info['name'] and proc.info['name'].lower() == workflow['process_name'].lower()]
            # 检查窗口
            hwnd = find_window_by_title(workflow['window_title'])
            # 只有进程和窗口都存在才算已启动
            if pids and hwnd:
                running_processes.append(f"✓ {workflow['name']}")
            else:
                running_processes.append(f"✗ {workflow['name']}")
        self.update_status("\n".join(running_processes))
    
    def clear_log(self):
        """清空日志"""
        self.log_text.config(state=tk.NORMAL)
        self.log_text.delete(1.0, tk.END)
        self.log_text.config(state=tk.DISABLED)
    
    def start_monitoring(self):
        """开始监测"""
        if self.is_running:
            return

        self.is_running = True
        self.run_btn.config(state=tk.DISABLED, text="运行中...")
        self.stop_btn.config(state=tk.NORMAL)

        self.log_message("开始进程监测...")

        # 启动前检测当前状态
        self.update_process_status()

        # 在新线程中运行监测
        self.monitor_thread = threading.Thread(target=self.run_monitoring, daemon=True)
        self.monitor_thread.start()
    
    def stop_monitoring(self):
        """停止监测"""
        self.is_running = False
        self.run_btn.config(state=tk.NORMAL, text="▶ 开始监测")
        self.stop_btn.config(state=tk.DISABLED)
        self.log_message("监测已停止")
    
    def run_monitoring(self):
        """运行监测逻辑"""
        try:
            # 检查MuMu任务状态
            if all_mumu_not_running(self.workflows):
                self.log_message("前四个MuMu任务均未运行，尝试执行一键启动批处理...")
                subprocess.Popen(r'E:\Date Of Computer\Desktop\Mard\yijian.bat', shell=True)
                self.log_message("已执行yijian.bat，等待60秒...")
                time.sleep(60)
            
            # 处理不可并行任务
            for wf in self.workflows:
                if not self.is_running:
                    break
                    
                if not wf.get('can_parallel', True):
                    self.process_workflow(wf, "不可并行任务")
            
            # 处理可并行任务
            for wf in self.workflows:
                if not self.is_running:
                    break
                    
                if wf.get('can_parallel', True):
                    self.process_workflow(wf, "可并行任务")
            
            self.log_message("所有任务处理完成")
            
        except Exception as e:
            self.log_message(f"监测过程中发生错误: {e}")
        finally:
            if self.is_running:
                self.stop_monitoring()
    
    def process_workflow(self, wf, task_type):
        """处理单个工作流"""
        # 等待MuMu任务完成
        while all_mumu_running_count(self.workflows) > 1 and wf not in self.workflows[:4]:
            if not self.is_running:
                return
            self.log_message("等待MuMu任务完成...")
            time.sleep(5)

        # 启动前检测进程状态
        if is_process_running(wf['process_name']):
            self.log_message(f"{wf['name']} 已在运行，跳过")
            # 更新状态显示
            self.update_process_status()
            return

        self.log_message(f"启动{task_type}：{wf['name']}")

        if wf.get('special', False):
            handle_special_task(wf)
        else:
            launch_and_wait(wf['launch_cmd'], wf['process_name'], wf['window_title'])
            ast_cmd = wf.get('ast_cmd')
            if ast_cmd:
                self.log_message(f"启动AST：{ast_cmd}")
                subprocess.Popen(quote_path(ast_cmd), shell=True)

        # 启动后检测进程状态并更新显示
        self.log_message(f"检测 {wf['name']} 启动状态...")
        self.update_process_status()

        self.log_message(f"等待{self.pair_interval}秒后继续...")
        time.sleep(self.pair_interval)
    
    def handle_workflow_action(self, workflow):
        """处理工作流动作"""
        self.log_message(f"手动启动任务: {workflow['name']}")

        # 在新线程中执行单个工作流
        def run_single_workflow():
            try:
                # 启动前检测进程状态
                if is_process_running(workflow['process_name']):
                    self.log_message(f"{workflow['name']} 已在运行")
                    # 更新状态显示
                    self.update_process_status()
                    return

                self.log_message(f"正在启动 {workflow['name']}...")

                if workflow.get('special', False):
                    handle_special_task(workflow)
                else:
                    success = launch_and_wait(
                        workflow['launch_cmd'],
                        workflow['process_name'],
                        workflow['window_title']
                    )

                    if success:
                        self.log_message(f"{workflow['name']} 启动成功")

                        # 启动辅助工具
                        ast_cmd = workflow.get('ast_cmd')
                        if ast_cmd:
                            self.log_message(f"启动辅助工具: {ast_cmd}")
                            subprocess.Popen(quote_path(ast_cmd), shell=True)
                    else:
                        self.log_message(f"{workflow['name']} 启动失败")

                # 启动后检测进程状态并更新显示
                self.log_message(f"检测 {workflow['name']} 启动状态...")
                self.update_process_status()

            except Exception as e:
                self.log_message(f"启动 {workflow['name']} 时发生错误: {e}")
                # 发生错误时也更新状态
                self.update_process_status()

        # 在后台线程中运行
        threading.Thread(target=run_single_workflow, daemon=True).start()

    def show_workflow_settings(self, workflow):
        """显示工作流设置"""
        settings_window = tk.Toplevel(self.root)
        settings_window.title(f"{workflow['name']} - 设置")
        settings_window.geometry("500x400")
        settings_window.configure(bg='white')
        settings_window.resizable(False, False)

        # 标题
        title_label = tk.Label(settings_window, text=f"{workflow['name']} 配置",
                              font=('微软雅黑', 14, 'bold'), bg='white')
        title_label.pack(pady=20)

        # 创建配置框架
        config_frame = tk.Frame(settings_window, bg='white')
        config_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

        # 显示配置信息
        configs = [
            ("任务名称", workflow['name']),
            ("进程名称", workflow['process_name']),
            ("窗口标题", workflow['window_title']),
            ("启动命令", workflow['launch_cmd']),
            ("辅助工具", workflow.get('ast_cmd', '无')),
            ("可并行", "是" if workflow.get('can_parallel', True) else "否"),
            ("特殊处理", "是" if workflow.get('special', False) else "否")
        ]

        for i, (label, value) in enumerate(configs):
            row_frame = tk.Frame(config_frame, bg='white')
            row_frame.pack(fill=tk.X, pady=5)

            tk.Label(row_frame, text=f"{label}:", font=('微软雅黑', 10, 'bold'),
                    bg='white', width=10, anchor='w').pack(side=tk.LEFT)

            # 创建文本框显示值
            text_widget = tk.Text(row_frame, height=1, font=('微软雅黑', 9),
                                 wrap=tk.WORD, bg='#f5f5f5')
            text_widget.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(10, 0))
            text_widget.insert(tk.END, str(value))
            text_widget.config(state=tk.DISABLED)

        # 按钮框架
        btn_frame = tk.Frame(settings_window, bg='white')
        btn_frame.pack(fill=tk.X, padx=20, pady=20)

        # 测试按钮
        test_btn = tk.Button(btn_frame, text="测试启动", bg='#2196F3', fg='white',
                            font=('微软雅黑', 10), relief=tk.FLAT,
                            command=lambda: self.test_workflow(workflow))
        test_btn.pack(side=tk.LEFT, padx=(0, 10))

        # 关闭按钮
        close_btn = tk.Button(btn_frame, text="关闭", bg='#757575', fg='white',
                             font=('微软雅黑', 10), relief=tk.FLAT,
                             command=settings_window.destroy)
        close_btn.pack(side=tk.RIGHT)

    def test_workflow(self, workflow):
        """测试工作流启动"""
        self.log_message(f"测试启动: {workflow['name']}")
        self.handle_workflow_action(workflow)

    def kill_workflow_process(self, workflow):
        pname = workflow['process_name']
        if self.kill_process_by_name(pname):
            # messagebox.showinfo("进程终止", f"{workflow['name']} 的进程已终止")
            self.log_message(f"已终止进程: {workflow['name']}")
            self.update_process_status()
        else:
            # messagebox.showwarning("终止失败", f"未找到或无法终止 {workflow['name']} 的进程")
            self.log_message(f"未找到或无法终止进程: {workflow['name']}")
            self.update_process_status()

    @staticmethod
    def kill_process_by_name(process_name):
        killed = False
        for proc in psutil.process_iter(['name']):
            if proc.info['name'] and proc.info['name'].lower() == process_name.lower():
                try:
                    proc.kill()
                    killed = True
                except Exception as e:
                    print(f"终止进程失败: {e}")
        return killed

    def log_running_processes(self):
        """输出所有在运行中的任务及其PID到日志（进程和窗口都存在才算）"""
        running_info = []
        for workflow in self.workflows:
            pids = [proc.info['pid'] for proc in psutil.process_iter(['name', 'pid']) if proc.info['name'] and proc.info['name'].lower() == workflow['process_name'].lower()]
            hwnd = find_window_by_title(workflow['window_title'])
            if pids and hwnd:
                running_info.append(f"{workflow['name']} 正在运行, PID: {', '.join(map(str, pids))}")
        if running_info:
            self.log_message("启动时检测到以下进程已在运行:")
            for info in running_info:
                self.log_message(info)
        else:
            self.log_message("启动时未检测到任何已在运行的任务进程")

    def start_status_timer(self):
        """启动状态定时更新"""
        self.update_process_status()
        # 每5秒更新一次状态
        self.root.after(5000, self.start_status_timer)
def main():
    root = tk.Tk()
    app = ProcessMonitorGUI(root)
    root.mainloop()

if __name__ == "__main__":
    main()
