#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GUI测试脚本 - 用于验证修复后的代码
"""

import sys
import os

def test_config_loading():
    """测试配置文件加载"""
    try:
        # 测试导入GUI模块
        from gui_interface import ProcessMonitorGUI
        import tkinter as tk
        
        print("✓ GUI模块导入成功")
        
        # 创建测试窗口
        root = tk.Tk()
        root.withdraw()  # 隐藏窗口，只测试初始化
        
        print("✓ Tkinter窗口创建成功")
        
        # 测试GUI初始化
        app = ProcessMonitorGUI(root)
        
        print("✓ GUI初始化成功")
        print(f"✓ 加载了 {len(app.workflows)} 个工作流任务")
        
        # 测试日志功能
        app.log_message("测试日志消息")
        print("✓ 日志功能正常")
        
        # 测试进程状态更新
        app.update_process_status()
        print("✓ 进程状态更新正常")
        
        root.destroy()
        print("✓ 所有测试通过！")
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    print("开始GUI测试...")
    print("=" * 50)
    
    if test_config_loading():
        print("=" * 50)
        print("GUI测试完成 - 所有功能正常！")
        print("现在可以安全运行 gui_interface.py")
    else:
        print("=" * 50)
        print("GUI测试失败 - 请检查错误信息")
        sys.exit(1)

if __name__ == "__main__":
    main()
