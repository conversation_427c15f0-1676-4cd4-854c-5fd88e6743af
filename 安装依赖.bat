@echo off
chcp 65001 >nul
title 安装依赖库

echo ========================================
echo         安装进程监测管理器依赖库
echo ========================================
echo.

echo 正在检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python环境
    echo 请确保已安装Python 3.6或更高版本
    pause
    exit /b 1
)

echo Python环境检查通过
echo.

echo 正在安装依赖库...
echo.

pip install -r requirements.txt

if errorlevel 1 (
    echo.
    echo 安装失败，请检查网络连接或Python环境
    echo 您也可以手动运行以下命令:
    echo pip install psutil pyautogui pywin32 pillow keyboard mouse
    pause
    exit /b 1
)

echo.
echo ========================================
echo         依赖库安装完成！
echo ========================================
echo.
echo 现在可以运行 "启动GUI.bat" 来启动程序
echo.
pause
