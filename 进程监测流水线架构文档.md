# 进程监测流水线架构文档

## 1. 项目概述

本项目是一个基于Python的自动化游戏管理系统，主要功能是监测和管理多个游戏进程的启动、运行状态监控以及自动化操作。系统采用模块化设计，集成了进程监测、OCR识别、窗口管理和自动化点击等核心功能。

### 1.1 项目结构
```
jumphelper/
├── process_monitor.py          # 主进程监测模块
├── ocr_click.py               # OCR识别与自动点击模块
├── config.json                # 主配置文件
├── ocr_click_config.json      # OCR配置文件
├── PaddleOCR/                 # OCR引擎目录
│   ├── PaddleOCR-json.exe     # OCR识别执行文件
│   └── models/                # OCR模型文件
├── jumpAssistante/            # 辅助工具目录
│   ├── main.py               # 自动点击脚本
│   └── get_mouse_position.py # 鼠标位置获取工具
└── 1.py                      # 测试文件
```

## 2. 核心架构概述

### 2.1 系统架构图
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   配置管理层     │    │   进程监测层     │    │   自动化执行层   │
│                │    │                │    │                │
│ config.json    │───▶│process_monitor.py│───▶│  ocr_click.py  │
│ocr_click_config│    │                │    │                │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │                        │
                              ▼                        ▼
                    ┌─────────────────┐    ┌─────────────────┐
                    │   系统接口层     │    │   OCR识别层     │
                    │                │    │                │
                    │ Windows API    │    │  PaddleOCR     │
                    │ psutil         │    │                │
                    └─────────────────┘    └─────────────────┘
```

### 2.2 数据流向
1. **配置读取** → 系统启动时读取config.json和ocr_click_config.json
2. **进程检测** → 使用psutil监测目标进程运行状态
3. **窗口管理** → 通过Windows API获取窗口句柄和状态
4. **OCR识别** → 调用PaddleOCR进行文字识别
5. **自动操作** → 基于识别结果执行自动点击操作

## 3. 功能模块详细说明

### 3.1 进程监测模块 (process_monitor.py)

#### 3.1.1 核心功能
- **进程状态检测**: 使用`psutil`库实时监测指定进程的运行状态
- **进程启动管理**: 自动启动配置的游戏进程和辅助工具
- **并发控制**: 管理可并行和不可并行任务的执行顺序
- **特殊任务处理**: 针对特定游戏的复杂启动流程

#### 3.1.2 关键函数
```python
# 进程状态检测
def is_process_running(process_name)

# 启动进程并等待
def launch_and_wait(launch_cmd, process_name, window_title, timeout=60)

# 窗口查找和激活
def find_window_by_title(title)
def activate_window(hwnd)

# 特殊任务处理
def handle_special_task(task)
```

#### 3.1.3 工作流程
1. 读取config.json配置文件
2. 检查MuMu模拟器任务状态，必要时执行一键启动
3. 按优先级处理不可并行任务
4. 处理可并行任务
5. 为每个任务启动对应的辅助工具(AST)

### 3.2 OCR识别模块 (ocr_click.py)

#### 3.2.1 核心功能
- **窗口截图**: 获取指定窗口的屏幕截图
- **文字识别**: 调用PaddleOCR进行文字识别
- **智能点击**: 基于OCR结果定位并点击目标文字
- **文字修正**: 通过替换表修正OCR识别错误

#### 3.2.2 关键函数
```python
# 窗口截图
def screenshot_window(hwnd, save_path='window_capture.png')

# OCR识别
def ocr_image(image_path)

# 目标点击
def click_target(ocr_result, target, offset_x=0, offset_y=0, replace_map=None)
```

### 3.3 辅助工具模块 (jumpAssistante/)

#### 3.3.1 自动点击脚本 (main.py)
- 提供热键触发的随机坐标点击功能
- 支持自定义坐标范围和点击间隔
- 用于游戏中的重复性操作自动化

#### 3.3.2 鼠标位置获取工具 (get_mouse_position.py)
- 实时监听鼠标点击事件
- 获取点击坐标用于配置自动点击脚本
- 支持热键控制监听状态

## 4. 配置文件结构说明

### 4.1 主配置文件 (config.json)

```json
{
  "workflows": [
    {
      "name": "游戏名称",
      "process_name": "进程名.exe",
      "window_title": "窗口标题",
      "launch_cmd": "启动命令路径",
      "ast_cmd": "辅助工具路径",
      "can_parallel": true/false,
      "special": true/false
    }
  ],
  "pair_interval": 10
}
```

#### 4.1.1 参数说明
- **name**: 任务显示名称
- **process_name**: 目标进程名称，用于状态检测
- **window_title**: 窗口标题，用于窗口查找
- **launch_cmd**: 游戏启动命令完整路径
- **ast_cmd**: 辅助工具启动命令（可选）
- **can_parallel**: 是否可与其他任务并行运行
- **special**: 是否需要特殊处理流程
- **pair_interval**: 任务间隔时间（秒）

### 4.2 OCR配置文件 (ocr_click_config.json)

```json
{
  "process_name": "目标进程名",
  "target_text": "目标文字",
  "replace_map": {
    "识别错误文字": "正确文字"
  }
}
```

#### 4.2.1 作用与关联性
- **进程关联**: 指定需要OCR识别的目标进程
- **文字修正**: 通过replace_map修正OCR识别错误
- **智能识别**: 提高文字识别准确率，减少误操作
- **与主流程集成**: 在特殊任务处理中被process_monitor.py调用

## 5. 流水线工作流程

### 5.1 系统启动流程
```
系统启动 → 读取配置 → 检测MuMu状态 → 执行一键启动(如需要) → 
处理不可并行任务 → 处理可并行任务 → 启动辅助工具 → 监控运行状态
```

### 5.2 特殊任务处理流程（以碧蓝航线为例）
```
启动Alas → 等待Updater窗口 → 等待主窗口出现 → 激活窗口 → 
OCR识别"启动"按钮 → 点击启动 → 验证"停止"按钮出现 → 完成
```

### 5.3 OCR识别与点击流程
```
窗口截图 → 调用PaddleOCR → 解析识别结果 → 应用替换表修正 → 
定位目标文字 → 计算点击坐标 → 执行点击操作 → 返回结果
```

## 6. 技术栈与依赖

### 6.1 核心依赖库
- **psutil**: 进程和系统信息获取
- **win32gui/win32process**: Windows API调用
- **pyautogui**: 自动化鼠标键盘操作
- **PIL (Pillow)**: 图像处理和截图
- **keyboard/mouse**: 热键监听和鼠标事件
- **subprocess**: 外部程序调用

### 6.2 外部工具
- **PaddleOCR-json.exe**: OCR文字识别引擎
- **多语言模型支持**: 中文、英文、日文、韩文等

## 7. 需要完善的功能点

### 7.1 高优先级改进
1. **错误处理机制**
   - 增加异常捕获和恢复机制
   - 添加日志记录系统
   - 实现进程崩溃自动重启

2. **配置管理优化**
   - 支持配置文件热重载
   - 添加配置验证机制
   - 实现图形化配置界面

3. **监控能力增强**
   - 添加进程资源使用监控
   - 实现运行状态可视化
   - 增加性能指标统计

### 7.2 中优先级改进
1. **OCR识别优化**
   - 支持多区域同时识别
   - 增加识别置信度阈值
   - 实现自适应识别参数

2. **任务调度优化**
   - 实现更灵活的依赖关系管理
   - 支持条件触发任务
   - 添加任务优先级队列

3. **用户交互改进**
   - 开发Web管理界面
   - 添加实时状态通知
   - 实现远程控制功能

### 7.3 低优先级改进
1. **扩展性增强**
   - 支持插件化架构
   - 添加自定义脚本支持
   - 实现多平台兼容

2. **安全性提升**
   - 添加权限控制机制
   - 实现操作审计日志
   - 增加数据加密存储

## 8. 实施计划与优先级

### 8.1 第一阶段（1-2周）- 稳定性提升
- [ ] 完善异常处理机制
- [ ] 添加日志记录系统
- [ ] 实现配置文件验证
- [ ] 优化进程监测逻辑

### 8.2 第二阶段（2-3周）- 功能增强
- [ ] 开发Web管理界面
- [ ] 实现状态监控面板
- [ ] 添加OCR识别优化
- [ ] 完善任务调度机制

### 8.3 第三阶段（3-4周）- 扩展优化
- [ ] 实现插件化架构
- [ ] 添加远程控制功能
- [ ] 优化用户体验
- [ ] 完善文档和测试

## 9. 风险评估与应对

### 9.1 技术风险
- **OCR识别准确率**: 通过替换表和多次尝试机制降低风险
- **进程监测稳定性**: 增加重试机制和异常恢复
- **系统兼容性**: 测试多版本Windows系统兼容性

### 9.2 运维风险
- **配置错误**: 实现配置验证和备份机制
- **资源占用**: 监控系统资源使用情况
- **权限问题**: 提供管理员权限运行指导

## 10. 总结

本进程监测流水线系统已具备基本的自动化游戏管理功能，通过模块化设计实现了进程监测、OCR识别和自动化操作的有机结合。ocr_click_config.json作为OCR识别的核心配置文件，通过替换表机制有效提升了文字识别准确率，与主流程形成了良好的协作关系。

系统的主要优势在于其灵活的配置机制和强大的扩展性，能够适应不同游戏的启动和管理需求。未来的改进重点应放在稳定性提升、用户体验优化和功能扩展上，以构建更加完善和可靠的自动化管理平台。
