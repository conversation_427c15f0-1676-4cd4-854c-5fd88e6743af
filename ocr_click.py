import json
import time
import subprocess
import pyautogui
from PIL import ImageGrab
import win32gui
import win32process
import psutil
import os

# 读取配置
with open('ocr_click_config.json', 'r', encoding='utf-8') as f:
    config = json.load(f)
process_name = config['process_name']
target_text = config['target_text']
replace_map = config.get('replace_map', {})

# 查找进程窗口句柄
def get_hwnd_by_process_name(proc_name):
    hwnds = []
    def callback(hwnd, hwnds):
        if win32gui.IsWindowVisible(hwnd):
            _, pid = win32process.GetWindowThreadProcessId(hwnd)
            try:
                p = psutil.Process(pid)
                if p.name().lower() == proc_name.lower():
                    hwnds.append(hwnd)
            except Exception:
                pass
        return True
    win32gui.EnumWindows(callback, hwnds)
    return hwnds[0] if hwnds else None

# 截图窗口
def screenshot_window(hwnd, save_path='window_capture.png'):
    left, top, right, bottom = win32gui.GetWindowRect(hwnd)
    img = ImageGrab.grab(bbox=(left, top, right, bottom))
    img.save(save_path)
    return save_path, left, top

# 调用PaddleOCR-json.exe识别
def ocr_image(image_path):
    exe_path = os.path.join('PaddleOCR', 'PaddleOCR-json.exe')
    result = subprocess.run([exe_path, image_path], capture_output=True, text=True)
    try:
        ocr_result = json.loads(result.stdout)
        return ocr_result
    except Exception:
        print('OCR识别失败:', result.stdout)
        return None

# 查找目标文字并点击
def click_target(ocr_result, target, offset_x=0, offset_y=0, replace_map=None):
    if replace_map is None:
        replace_map = {}
    for item in ocr_result.get('data', []):
        ocr_text = item.get('text', '')
        # 替换表修正
        fixed_text = replace_map.get(ocr_text, ocr_text)
        if fixed_text == target:
            box = item['box']
            # box: [x1, y1, x2, y2, x3, y3, x4, y4]
            x = int((box[0] + box[2] + box[4] + box[6]) / 4) + offset_x
            y = int((box[1] + box[3] + box[5] + box[7]) / 4) + offset_y
            pyautogui.click(x, y)
            print(f'已点击目标文字: {target}，坐标: ({x},{y})')
            return True
    print(f'未找到目标文字: {target}')
    return False

if __name__ == '__main__':
    hwnd = get_hwnd_by_process_name(process_name)
    if not hwnd:
        print(f'未找到进程窗口: {process_name}')
        exit(1)
    img_path, left, top = screenshot_window(hwnd)
    ocr_result = ocr_image(img_path)
    if ocr_result:
        click_target(ocr_result, target_text, offset_x=left, offset_y=top, replace_map=replace_map) 