# 进程监测管理器 GUI 使用说明

## 快速开始

### 1. 安装依赖
双击运行 `安装依赖.bat` 文件，自动安装所需的Python库。

### 2. 启动程序
双击运行 `启动GUI.bat` 文件，启动图形化界面。

## 界面说明

### 左侧控制面板

#### 🔄 运行区域
- **开始监测**: 启动进程监测流水线，自动管理所有配置的游戏进程

#### ⏳ 等待区域
- **动态任务列表**: 自动从config.json加载所有配置的游戏任务
- **🎮 特殊任务**: 带游戏手柄图标，表示需要特殊处理的任务
- **📱 普通任务**: 带手机图标，表示标准启动流程的任务
- **手动启动**: 点击任务名称可单独启动该任务
- **⚙ 设置按钮**: 点击可查看和测试对应任务的详细配置

#### 🚫 终止区域
- **停止监测**: 停止当前运行的监测流程

#### 📊 状态区域
- 实时显示各个游戏进程的运行状态（每5秒自动刷新）
- ✓ 表示进程正在运行
- ✗ 表示进程未运行
- 显示config.json中所有配置任务的状态

### 右侧日志面板

#### 📋 日志区域
- 显示详细的操作日志和状态信息
- 包含时间戳的操作记录
- **🗑 清空按钮**: 清空当前日志内容

## 功能特性

### 自动化监测
- 自动检测MuMu模拟器状态
- 智能管理并行和串行任务
- 支持特殊任务的复杂启动流程

### 实时状态监控
- 实时显示进程运行状态
- 详细的操作日志记录
- 直观的状态指示器

### 灵活的任务管理
- 动态加载config.json中的所有任务
- 支持手动启动单个任务
- 详细的任务配置查看和测试功能
- 智能的任务调度机制
- 区分特殊任务和普通任务的处理流程

## API集成说明

GUI界面已集成以下核心API功能：

### process_monitor.py 集成
- `is_process_running()`: 检查进程运行状态
- `launch_and_wait()`: 启动进程并等待
- `handle_special_task()`: 处理特殊任务流程
- `all_mumu_not_running()`: 检查MuMu任务状态
- `all_mumu_running_count()`: 统计运行中的MuMu任务

### 配置文件支持
- 自动读取 `config.json` 主配置文件
- 支持 `ocr_click_config.json` OCR配置
- 动态加载工作流配置

### 多线程支持
- 监测任务在后台线程运行
- 不阻塞GUI界面响应
- 安全的线程间通信

## 配置文件

确保以下配置文件存在于程序目录中：

- `config.json`: 主配置文件，包含游戏工作流定义
- `ocr_click_config.json`: OCR识别配置文件

## 故障排除

### 常见问题

1. **启动失败**
   - 检查Python环境是否正确安装
   - 运行 `安装依赖.bat` 确保依赖库完整

2. **配置文件错误**
   - 确保 `config.json` 格式正确
   - 检查文件路径是否存在

3. **进程检测失败**
   - 确保目标进程名称正确
   - 检查进程是否有足够权限

### 日志分析
- 查看右侧日志面板的详细信息
- 注意错误消息和时间戳
- 根据日志信息定位问题

## 技术支持

如遇到问题，请：
1. 查看日志面板的错误信息
2. 检查配置文件格式
3. 确认依赖库安装完整
4. 以管理员权限运行程序

## 更新日志

### v1.0.0
- 初始版本发布
- 基础进程监测功能
- 图形化界面支持
- API集成完成
