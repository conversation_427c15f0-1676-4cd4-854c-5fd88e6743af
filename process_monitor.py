import json
import subprocess
import time
import psutil
import win32gui
import win32process
import pyautogui
import win32con
from ocr_click import screenshot_window, ocr_image, click_target

def quote_path(cmd):
    parts = cmd.strip().split(' ', 1)
    exe = parts[0]
    rest = parts[1] if len(parts) > 1 else ''
    if not (exe.startswith('"') and exe.endswith('"')):
        exe = f'"{exe}"'
    return f'{exe} {rest}'.strip()

# 检查进程是否存在
def is_process_running(process_name):
    for proc in psutil.process_iter(['name']):
        if proc.info['name'] and proc.info['name'].lower() == process_name.lower():
            return True
    return False

# 启动并等待窗口出现
def launch_and_wait(launch_cmd, process_name, window_title, timeout=60):
    subprocess.Popen(quote_path(launch_cmd), shell=True)
    print(f"已启动：{launch_cmd}，等待窗口标题“{window_title}”出现...")
    for _ in range(timeout):
        if is_process_running(process_name):
            print(f"进程“{process_name}”已检测到！")
            return True
        time.sleep(1)
    print(f"超时未检测到进程“{process_name}”，可重试或报错。")
    return False

# 查找窗口句柄
def find_window_by_title(title):
    def callback(hwnd, result):
        if win32gui.IsWindowVisible(hwnd) and title in win32gui.GetWindowText(hwnd):
            result.append(hwnd)
    result = []
    win32gui.EnumWindows(callback, result)
    return result[0] if result else None

# 激活窗口
def activate_window(hwnd):
    win32gui.ShowWindow(hwnd, win32con.SW_RESTORE)
    win32gui.SetForegroundWindow(hwnd)

# 特殊任务处理
def handle_special_task(task):
    if task['name'] == "碧蓝航线":
        print("[特殊任务] 启动碧蓝航线流程")
        # 启动 Alas
        subprocess.Popen(quote_path(task['launch_cmd']), shell=True)
        print("等待 Alas Updater 窗口出现...")
        # 等待 Alas Updater 出现
        for _ in range(60):
            updater_hwnd = find_window_by_title("Alas Updater")
            if updater_hwnd:
                print("检测到 Alas Updater 窗口")
                break
            time.sleep(1)
        else:
            print("未检测到 Alas Updater，流程中止")
            return
        # 等待 Alas Updater 消失，Alas 主窗口出现
        print("等待 Alas Updater 消失并检测 Alas 主窗口...")
        for _ in range(120):
            updater_hwnd = find_window_by_title("Alas Updater")
            alas_hwnd = find_window_by_title("Alas")
            if not updater_hwnd and alas_hwnd:
                print("Alas Updater 已消失，Alas 主窗口已出现")
                break
            time.sleep(1)
        else:
            print("Alas 主窗口未出现，流程中止")
            return
        # 激活 Alas 主窗口
        activate_window(alas_hwnd)
        time.sleep(1)
        # OCR 识别“启动”
        # 读取替换表
        try:
            with open('ocr_click_config.json', 'r', encoding='utf-8') as f:
                ocr_config = json.load(f)
            replace_map = ocr_config.get('replace_map', {})
        except Exception:
            replace_map = {}
        img_path, left, top = screenshot_window(alas_hwnd)
        ocr_result = ocr_image(img_path)
        found = False
        if ocr_result:
            found = click_target(ocr_result, "启动", offset_x=left, offset_y=top, replace_map=replace_map)
        if not found:
            # 依次点击两个坐标
            for x, y in [(528, 79), (579, 114)]:
                pyautogui.click(left + x, top + y)
                print(f"未找到'启动'，点击了({left + x},{top + y})")
                time.sleep(0.5)
        # 点击后等待1秒，再OCR识别“停止”
        time.sleep(1)
        img_path, left, top = screenshot_window(alas_hwnd)
        ocr_result = ocr_image(img_path)
        if ocr_result and click_target(ocr_result, "停止", offset_x=left, offset_y=top, replace_map=replace_map):
            print("检测到'停止'，碧蓝航线启动成功！")
        else:
            print("未检测到'停止'，请检查启动情况。")
        return
    # 其他特殊任务占位
    print(f"[特殊任务占位] {task['name']} 需要特殊处理，暂未实现。")

def all_mumu_not_running(workflows):
    # 只检查前四个MuMu任务
    for wf in workflows[:4]:
        if is_process_running(wf['process_name']):
            return False
    return True

def all_mumu_running_count(workflows):
    # 返回前四个MuMu任务正在运行的数量
    count = 0
    for wf in workflows[:4]:
        if is_process_running(wf['process_name']):
            count += 1
    return count

def main():
    with open('config.json', 'r', encoding='utf-8') as f:
        config = json.load(f)
    workflows = config['workflows']
    pair_interval = config.get('pair_interval', 10)

    # 先尝试一键启动前四个MuMu任务
    if all_mumu_not_running(workflows):
        print("前四个MuMu任务均未运行，尝试执行一键启动批处理...")
        # 这里不能加quote_path，否则会多余引号导致bat无法执行
        subprocess.Popen(r'E:\Date Of Computer\Desktop\Mard\yijian.bat', shell=True)
        print("已执行yijian.bat，等待60秒让模拟器和脚本全部启动...")
        time.sleep(60)

    # 先处理不可并行任务
    for wf in workflows:
        if not wf.get('can_parallel', True):
            # 只要前四个MuMu任务还在运行超过1个，就等待
            while all_mumu_running_count(workflows) > 1 and wf not in workflows[:4]:
                print("前四个MuMu任务仍有多个在运行，等待其结束后再启动后续任务...")
                time.sleep(5)
            if is_process_running(wf['process_name']):
                print(f"{wf['name']} 已在运行，跳过。")
                continue
            if wf.get('special', False):
                handle_special_task(wf)
            else:
                print(f"启动不可并行任务：{wf['name']}")
                launch_and_wait(wf['launch_cmd'], wf['process_name'], wf['window_title'])
                ast_cmd = wf.get('ast_cmd')
                if ast_cmd:
                    print(f"启动AST：{ast_cmd}")
                    subprocess.Popen(quote_path(ast_cmd), shell=True)
            print(f"等待{pair_interval}秒后继续...")
            time.sleep(pair_interval)

    # 再处理可并行任务
    for wf in workflows:
        if wf.get('can_parallel', True):
            # 只要前四个MuMu任务还在运行超过1个，就等待
            while all_mumu_running_count(workflows) > 1 and wf not in workflows[:4]:
                print("前四个MuMu任务仍有多个在运行，等待其结束后再启动后续任务...")
                time.sleep(5)
            if is_process_running(wf['process_name']):
                print(f"{wf['name']} 已在运行，跳过。")
                continue
            if wf.get('special', False):
                handle_special_task(wf)
            else:
                print(f"启动可并行任务：{wf['name']}")
                subprocess.Popen(quote_path(wf['launch_cmd']), shell=True)
                ast_cmd = wf.get('ast_cmd')
                if ast_cmd:
                    print(f"启动AST：{ast_cmd}")
                    subprocess.Popen(quote_path(ast_cmd), shell=True)
            print(f"等待{pair_interval}秒后继续...")
            time.sleep(pair_interval)

if __name__ == "__main__":
    main() 