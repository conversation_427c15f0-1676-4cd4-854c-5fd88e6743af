#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
进程监测管理器 GUI 启动脚本
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox

def check_dependencies():
    """检查依赖库是否安装"""
    required_modules = [
        'psutil',
        'pyautogui', 
        'win32gui',
        'win32process',
        'win32con',
        'PIL'
    ]
    
    missing_modules = []
    for module in required_modules:
        try:
            __import__(module)
        except ImportError:
            missing_modules.append(module)
    
    if missing_modules:
        error_msg = f"缺少以下依赖库:\n{', '.join(missing_modules)}\n\n"
        error_msg += "请运行以下命令安装:\n"
        error_msg += "pip install psutil pyautogui pywin32 pillow"
        
        root = tk.Tk()
        root.withdraw()
        messagebox.showerror("依赖检查失败", error_msg)
        return False
    
    return True

def check_config_files():
    """检查配置文件是否存在"""
    config_files = ['config.json', 'ocr_click_config.json']
    missing_files = []
    
    for file in config_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        error_msg = f"缺少以下配置文件:\n{', '.join(missing_files)}\n\n"
        error_msg += "请确保配置文件存在于当前目录中"
        
        root = tk.Tk()
        root.withdraw()
        messagebox.showerror("配置文件检查失败", error_msg)
        return False
    
    return True

def main():
    """主函数"""
    print("正在启动进程监测管理器...")
    
    # 检查依赖
    if not check_dependencies():
        sys.exit(1)
    
    # 检查配置文件
    if not check_config_files():
        sys.exit(1)
    
    try:
        # 导入并启动GUI
        from gui_interface import ProcessMonitorGUI
        
        root = tk.Tk()
        app = ProcessMonitorGUI(root)
        
        print("GUI界面已启动")
        root.mainloop()
        
    except Exception as e:
        root = tk.Tk()
        root.withdraw()
        messagebox.showerror("启动失败", f"启动GUI时发生错误:\n{str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
